import { getAdStrategy } from '@/services/ibidder_api/operation';
import { Button, Typography, Modal, Segmented } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSearchParams, useModel } from '@umijs/max';
import dayjs from 'dayjs';
import styles from './style.less';
import { Real_ads_result, WeekStrategyData } from '../AiWork/components/weekStrategy';
import WeekStrategyContent from '../AiWork/components/weekStrategy';
import { getCountryTimezone, getDayOfWeek, shouldShowConfirmationSection } from '@/utils/bus';
import WeekMonthAnalysisContent, { WeekAnalysisData } from '../AiWork/components/weekAnalysis';
import DayStrategyContent, { DaypartAdsAdjustments, DayStrategyData } from '../AiWork/components/dayStragegy';
import { monthly_trends } from '../MonthlyTrends';
// import IMGgoToReview from '@/assets/images/goToReview.svg';
import IMGcustomizing from '@/assets/images/customizing.svg';
import { EditOutlined } from '@ant-design/icons';
const { Title, Text } = Typography;

interface AdStrategyProps {
  country: string;
  onDocIdListUpdate: (docIdList: string[]) => void;
}

const AdStrategy: React.FC<AdStrategyProps> = ({ country, onDocIdListUpdate }) => {
  const { refreshCount } = useModel('strategyRefresh');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const [monthStrategyData, setMonthStrategyData] = useState<API.GetAdStrategyResponseData<{ market_report_month: WeekAnalysisData, market_trends: { monthly_trends: monthly_trends } }> | null>(null);
  const [weekStrategyData, setWeekStrategyData] = useState<API.GetAdStrategyResponseData<{ ads_strategy_week: WeekStrategyData, real_ads_result: Real_ads_result }> | null>(null);
  const [dayStrategyData, setDayStrategyData] = useState<API.GetAdStrategyResponseData<{ ads_strategy_day: DayStrategyData, real_ads_result: Real_ads_result }> | null>(null);
  const [hasMonthReport, setHasMonthReport] = useState(true)
  const [hasWeekReport, setHasWeekReport] = useState(true)
  const [daypartAdsAdjustments, setDaypartAdsAdjustments] = useState<DaypartAdsAdjustments | null>(null);

  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [isMonthModalVisible, setIsMonthModalVisible] = useState(false);
  const [isDayModalVisible, setIsDayModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'day' | 'week' | 'month'>('day');

  const getStrategyData = async (activeKey: 'day' | 'week' | 'month' | undefined) => {
    const results = await Promise.all([
      getAdStrategy({
        date: getCountryTimezone(country, 'YYYY-MM-DD'),
        job_id: 'market_report_month',
        asin: parent_asin,
        profile_id,
      }),
      getAdStrategy({
        date: getCountryTimezone(country, 'YYYY-MM-DD'),
        job_id: 'ads_strategy_week',
        asin: parent_asin,
        profile_id,
      }),
      getAdStrategy({
        date: getCountryTimezone(country, 'YYYY-MM-DD'),
        job_id: 'ads_strategy_day',
        asin: parent_asin,
        profile_id,
      }),
    ]);

    const [monthRes, weekRes, dayRes] = results as [any, any, any];
    let monthData = null;
    let weekData = null;
    let dayData = null;

    if (monthRes.code === 200) {
      setMonthStrategyData(monthRes.data);
      monthData = monthRes.data;
    }

    if (weekRes.code === 200) {
      setWeekStrategyData(weekRes.data);
      weekData = weekRes.data;
      setHasMonthReport(true)
    } else if (weekRes.code === 404) {
      setHasMonthReport(weekRes.data.is_last_report_reviewed ?? true)
    }

    if (dayRes.code === 200) {
      setDayStrategyData(dayRes.data);
      setDaypartAdsAdjustments(dayRes.data.daypart_ads_adjustments);
      dayData = dayRes.data;
      setHasWeekReport(true)
    } else if (dayRes.code === 404) {
      setHasWeekReport(dayRes.data.is_last_report_reviewed ?? true)
    }

    onDocIdListUpdate([monthData?.es_id, weekData?.es_id, dayData?.es_id].filter(Boolean) as string[]);

    if (activeKey) {
      setActiveTab(activeKey)
    } else {
      // 根据数据可用性设置默认显示的标签页
      if (dayData) {
        setActiveTab('day');
      } else if (weekData) {
        setActiveTab('week');
      } else if (monthData) {
        setActiveTab('month');
      }
    }
  }

  // 获取策略看板数据
  useEffect(() => {
    getStrategyData(undefined);
  }, [parent_asin, refreshCount]);

  const viewMonthStrategy = () => {
    setIsMonthModalVisible(true);
  };

  const viewCompleteStrategy = () => {
    if (weekStrategyData) {
      setIsModalVisible(true);
    }
  };

  const viewDayStrategy = () => {
    setIsDayModalVisible(true);
  };

  const market_report_month = monthStrategyData?.result?.market_report_month;
  const ads_strategy_week = weekStrategyData?.result?.ads_strategy_week;
  const week_real_ads_result = weekStrategyData?.result?.real_ads_result;
  const day_real_ads_result = dayStrategyData?.result?.real_ads_result;
  const ads_strategy_day = dayStrategyData?.result?.ads_strategy_day;
  const renderContent = () => {
    switch (activeTab) {
      case 'month':
        return (
          <div>
            {monthStrategyData && (
              <WeekMonthAnalysisContent
                key={monthStrategyData.es_id + monthStrategyData.current_time}
                asin={parent_asin}
                asins={monthStrategyData.asins}
                parent_asin={parent_asin}
                esId={monthStrategyData.es_id}
                profile_id={profile_id}
                current_time={monthStrategyData.current_time}
                job_id={monthStrategyData.job_id}
                country={country}
                role={monthStrategyData.role}
                type="month"
                start_date={market_report_month?.start_date ?? ''}
                isEdit={monthStrategyData.can_edit}
                monthly_trends={monthStrategyData.result.market_trends.monthly_trends}
                onCancel={() => { }}
                onSuccess={() => { }}
                controlBtn={
                  monthStrategyData?.can_edit === true && shouldShowConfirmationSection(country, 'month') &&
                  <Button onClick={viewMonthStrategy}><EditOutlined />修改策略</Button>
                }
                showReviewArea={false}
              />
            )}
          </div>
        );
      case 'week':
        return (
          weekStrategyData === null ?
            <div className={styles.reportGoToReview} >
              <img src={IMGcustomizing} />
              {hasMonthReport === false ? <Text>请先审核月市场报告</Text> : <Text>周广告策略正在制定中，请稍后～</Text>}
              {hasMonthReport === false ? <Button type='primary' onClick={monthStrategyData?.can_edit === true ? viewMonthStrategy : undefined} >去审核</Button> : null}
            </div>
            :
            <div>
              {weekStrategyData && (
                <WeekStrategyContent
                  key={weekStrategyData.es_id + weekStrategyData.current_time}
                  asin={parent_asin}
                  profile_id={profile_id}
                  date={dayjs(new Date()).format('YYYY-MM-DD')}
                  current_time={weekStrategyData.current_time}
                  job_id={weekStrategyData.job_id}
                  country={country}
                  role={weekStrategyData.role}
                  isCompleteStrategy={true}
                  esId={weekStrategyData.es_id}
                  isEdit={weekStrategyData.can_edit}
                  real_ads_result={week_real_ads_result}
                  start_date={ads_strategy_week?.start_date ?? ''}
                  initialData={ads_strategy_week}
                  onCancel={() => { }}
                  onSuccess={() => { }}
                  controlBtn={
                    weekStrategyData?.can_edit === true && shouldShowConfirmationSection(country, 'week') &&
                    <Button onClick={viewCompleteStrategy}><EditOutlined />修改策略</Button>
                  }
                  showReviewArea={false}
                />
              )}
            </div>
        );
      case 'day':
        return (
          dayStrategyData === null ?
            <div className={styles.reportGoToReview} >
              <img src={IMGcustomizing} />
              {hasMonthReport === false ? <Text>请先审核月市场报告</Text> :
                hasWeekReport === false ? <Text>请先审核周广告策略</Text> : <Text>日广告策略正在制定中，请稍后～</Text>}
              {
                hasMonthReport === false ? <Button type='primary' onClick={monthStrategyData?.can_edit === true ? viewMonthStrategy : undefined} >去审核</Button> :
                  hasWeekReport === false ? <Button type='primary' onClick={weekStrategyData?.can_edit === true ? viewCompleteStrategy : undefined} >去审核</Button> : null
              }
            </div>
            :
            <div>
              {dayStrategyData && (
                <DayStrategyContent
                  key={dayStrategyData.es_id + dayStrategyData.current_time}
                  asin={parent_asin}
                  esId={dayStrategyData.es_id}
                  profile_id={profile_id}
                  current_time={dayStrategyData.current_time}
                  job_id={dayStrategyData.job_id}
                  asins={dayStrategyData.asins}
                  country={country}
                  role={dayStrategyData.role}
                  real_ads_result={day_real_ads_result}
                  isEdit={dayStrategyData.can_edit}
                  onCancel={() => { }}
                  onSuccess={() => { }}
                  date={dayStrategyData?.result?.ads_strategy_day?.date ?? ''}
                  daypart_ads_adjustments={daypartAdsAdjustments}
                  controlBtn={
                    dayStrategyData?.can_edit === true && shouldShowConfirmationSection(country, 'day', dayStrategyData?.result?.ads_strategy_day?.date) &&
                    <Button onClick={viewDayStrategy}><EditOutlined />修改策略</Button>
                  }
                  showReviewArea={false}
                />
              )}
            </div>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className={styles.container}>
      <div className={styles.viewSwitcher}>
        <Segmented
          block
          size='large'
          value={activeTab}
          onChange={(value) => setActiveTab(value as 'day' | 'week' | 'month')}
          options={[
            { label: `今日 ${ads_strategy_day?.date ? `(${ads_strategy_day.date} ${getDayOfWeek(ads_strategy_day.date)})` : ''}`, value: 'day' },
            { label: `本周 ${ads_strategy_week?.start_date && ads_strategy_week?.end_date ? `(${ads_strategy_week.start_date}~${ads_strategy_week.end_date})` : ''}`, value: 'week' },
            { label: `本月 ${market_report_month?.start_date && market_report_month?.end_date ? `(${market_report_month.start_date}~${market_report_month.end_date})` : ''}`, value: 'month' },
          ]}
        />
      </div>

      {/* 内容区域 */}
      <div className={styles.renderContent}>
        {renderContent()}
      </div>

      {/* Modal组件保持不变 */}
      <Modal
        title={<Title level={2} style={{ margin: 0 }}>月市场分析报告（{market_report_month?.start_date ?? ''}~{market_report_month?.end_date ?? ''}）</Title>}
        open={isMonthModalVisible}
        onCancel={() => setIsMonthModalVisible(false)}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {monthStrategyData && (
          <WeekMonthAnalysisContent
            key={monthStrategyData.es_id + monthStrategyData.current_time}
            asin={parent_asin}
            asins={monthStrategyData.asins}
            parent_asin={parent_asin}
            esId={monthStrategyData.es_id}
            profile_id={profile_id}
            current_time={monthStrategyData.current_time}
            job_id={monthStrategyData.job_id}
            country={country}
            role={monthStrategyData.role}
            start_date={market_report_month?.start_date ?? ''}
            type="month"
            isEdit={monthStrategyData.can_edit}
            monthly_trends={monthStrategyData.result.market_trends.monthly_trends}
            onCancel={() => setIsMonthModalVisible(false)}
            onSuccess={() => { getStrategyData(activeTab) }}
          />
        )}
      </Modal>

      <Modal
        title={<Title level={2} style={{ margin: 0 }}>周广告投放策略（{ads_strategy_week?.start_date ?? ''}~{ads_strategy_week?.end_date ?? ''}）</Title>}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {weekStrategyData && (
          <WeekStrategyContent
            key={weekStrategyData.es_id + weekStrategyData.current_time}
            asin={parent_asin}
            profile_id={profile_id}
            date={dayjs(new Date()).format('YYYY-MM-DD')}
            current_time={weekStrategyData.current_time}
            job_id={weekStrategyData.job_id}
            country={country}
            role={weekStrategyData.role}
            isCompleteStrategy={true}
            esId={weekStrategyData.es_id}
            isEdit={weekStrategyData.can_edit}
            initialData={ads_strategy_week}
            onCancel={() => setIsModalVisible(false)}
            start_date={ads_strategy_week?.start_date ?? ''}
            real_ads_result={week_real_ads_result}
            onSuccess={() => { getStrategyData(activeTab) }}
          />
        )}
      </Modal>

      <Modal
        title={<Title level={2} style={{ margin: 0 }}>日广告投放策略（{ads_strategy_day?.date ?? ''} {getDayOfWeek(ads_strategy_day?.date)}）</Title>}
        open={isDayModalVisible}
        onCancel={() => setIsDayModalVisible(false)}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {dayStrategyData && (
          <DayStrategyContent
            key={dayStrategyData.es_id + dayStrategyData.current_time}
            asin={parent_asin}
            esId={dayStrategyData.es_id}
            profile_id={profile_id}
            current_time={dayStrategyData.current_time}
            job_id={dayStrategyData.job_id}
            asins={dayStrategyData.asins}
            country={country}
            role={dayStrategyData.role}
            real_ads_result={day_real_ads_result}
            date={dayStrategyData?.result?.ads_strategy_day?.date ?? ''}
            daypart_ads_adjustments={daypartAdsAdjustments}
            isEdit={dayStrategyData.can_edit}
            onCancel={() => setIsDayModalVisible(false)}
            onSuccess={() => { getStrategyData(activeTab) }}
          />
        )}
      </Modal>
    </div>
  );
};

export default AdStrategy;
