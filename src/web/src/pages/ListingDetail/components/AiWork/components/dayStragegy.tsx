import React, { useState, useEffect } from 'react';
import { Card, Typography, Table, Empty, Tag, message, Flex, Select, Space, Button, Modal } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import Hod<PERSON><PERSON> from './HodChart';
import styles from './index.less';
import { editReport, getRoleAgentDetail, getAdStrategy } from '@/services/ibidder_api/operation';
import DayStrategyCard from '../../DayStrategyCard';
import DayStrategyExpectedResults from '../../DayStrategyExpectedResults';
import { Real_ads_result } from './weekStrategy';
import DateNavigator from './DateNavigator';
import RevisionHistory, { Revision_history } from './RevisionHistory';
import { approveReportWithMessageId, shouldShowConfirmationSection, generateVersionOptions } from '@/utils/bus';
import EmptyState from '@/pages/ListingDetail/components/EmptyState';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import WeeklyProgressAnalysis, { WeeklyProgressAnalysisProps } from './WeeklyProgressAnalysis';
import DayStrategyCatContent from './dayStrategyCat';
import { processNumberOrString, getDayOfWeek } from '@/utils/bus';

const { Title, Text, Paragraph } = Typography;

export interface DaypartAdsAdjustments {
  amount?: {
    new: number | null;
    old: number | null;
  };
  daypart_report: {
    job_id: string;
    current_time: string;
  };
  set_campaign_budget?: {
    set_campaign_budget: string | null;
  };
  set_placement_bidding?: {
    set_placement_bidding: string | null;
  };
}

export interface DayStrategyData {
  date: string;
  approach: string;
  rationale: string;
  day_budget: {
    amount: number;
    yesterday: number;
    change_from_yesterday: string;
    adjustment_range: {
      min: number;
      max: number;
    };
    rationale: string;
  };
  revision_history?: Revision_history;
  expected_results: {
    spend: number;
    orders: number;
    acos: number;
    sales: number;
    cvr: number;
  };
  campaign_budget: {
    campaign_id: string;
    amount: number;
    benchmark_budget?: number;
    change_from_weekly_avg: string;
    rationale: string;
    bid_adjustment?: number | string;
    campaign_name?: string;
    campaign_type?: string;
    l14d_acos?: string | number;
    swd_acos?: string | number;
  }[];
  hod_bid_adjustment: {
    hour: number;
    adjustment: number;
    rationale: string;
  }[];
  placement_bid_adjustment: {
    placement_type: string;
    old_bid: number;
    new_bid: number;
    adjustment_ratio: number;
    rationale: string;
  }[];
  weekly_progress_analysis: WeeklyProgressAnalysisProps;
  ai_feedbackContent?: string
}

interface DayStrategyContentProps {
  asin?: string;
  asins?: string[];
  parent_asin?: string;
  profile_id?: string;
  job_id?: string;
  target_job_id?: string;
  current_time?: string;
  esId?: string;
  isEdit?: boolean;
  onCancel?: (refresh: boolean) => void;
  onSuccess: () => void;
  country?: string;
  real_ads_result?: Real_ads_result;
  role?: string;
  showHistoryVersion?: boolean;
  showDateNavigator?: boolean;
  date?: string;
  controlBtn?: React.ReactNode;
  daypart_ads_adjustments?: DaypartAdsAdjustments | null;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

// 广告日策略组件
const DayStrategyContent: React.FC<DayStrategyContentProps> = (props) => {
  const {
    asin,
    asins,
    job_id,
    target_job_id,
    esId,
    onCancel = () => { },
    onSuccess,
    country,
    role,
    showHistoryVersion = true,
    showDateNavigator = true,
    real_ads_result,
    date,
    controlBtn,
    daypart_ads_adjustments,
    showReviewArea = true
  } = props

  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;

  const [es_id, setEs_id] = useState<string>('');
  const [latestEsId, setLatestEsId] = useState<string>(''); // 专门存储当前日期最新的esid
  const [isEdit, setIsEdit] = useState<boolean>(props.isEdit ?? false);
  const [current_time, setCurrent_time] = useState(props.current_time)
  const [dayStrategyData, setDayStrategyData] = useState<DayStrategyData | null>(null);
  const [_real_ads_result, setReal_ads_result] = useState<Real_ads_result | undefined>(real_ads_result);
  const [currentVersion, setCurrentVersion] = useState<number>(1);
  const [maxVersion, setMaxVersion] = useState<number>(1);
  const [versionOptions, setVersionOptions] = useState<{ label: string, value: number }[]>([]);

  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');
  const [daypartAdsAdjustments, setDaypartAdsAdjustments] = useState<DaypartAdsAdjustments | null>(daypart_ads_adjustments || null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState<boolean>(false);

  // 检查 daypartAdsAdjustments 是否有有效数据
  const hasValidAdjustmentData = (data: DaypartAdsAdjustments | null): boolean => {
    if (!data) return false;

    // 检查 amount 是否有有效数据
    const hasValidAmount = data.amount &&
      data.amount.new !== null &&
      data.amount.old !== null &&
      (data.amount.new !== data.amount.old);

    // 检查 set_campaign_budget 是否有有效数据
    const hasValidCampaignBudget = !!(data.set_campaign_budget?.set_campaign_budget &&
      data.set_campaign_budget.set_campaign_budget.trim() !== '');

    // 检查 set_placement_bidding 是否有有效数据
    const hasValidPlacementBidding = !!(data.set_placement_bidding?.set_placement_bidding &&
      data.set_placement_bidding.set_placement_bidding.trim() !== '');

    return !!(hasValidAmount || hasValidCampaignBudget || hasValidPlacementBidding);
  };

  // Callback to handle data updates from DateNavigator
  const handleStrategyDataUpdate = (data: any) => {
    setDayStrategyData(data.result.ads_strategy_day);
    setReal_ads_result(data.result.real_ads_result);
    setEs_id(data.es_id);
    setLatestEsId(data.es_id); // 保存最新的esid
    setIsEdit(data.can_edit);
    setDaypartAdsAdjustments(data.daypart_ads_adjustments);
    setCurrent_time(data.current_time);

    // 处理版本信息 - 只在这里设置版本
    processVersionInfo(data.es_id);
  };

  // 处理版本信息的独立方法 - 基于最新的esid处理版本
  const processVersionInfo = (esId: string) => {
    if (!esId) return;

    const parts = esId.split('_');
    if (parts.length > 1) {
      const version = parseInt(parts[1]);
      setMaxVersion(version); // 设置最大版本号
      setVersionOptions(generateVersionOptions(version)); // 版本选项只基于最新esid
      setCurrentVersion(version); // 设置当前版本为最新版本
    }
  };



  const fetchStrategyData = async () => {
    // 通过 getAdStrategy 接口获取报告数据
    if (asin && profile_id && date) {
      try {
        const res: any = await getAdStrategy({
          date: date,
          job_id: job_id || 'ads_strategy_day',
          asin,
          profile_id,
        });

        if (res.code === 200 && res.data?.result?.ads_strategy_day) {
          const strategyData = res.data.result.ads_strategy_day;
          setDayStrategyData(strategyData);
          setReal_ads_result(res.data.result.real_ads_result);
          setEs_id(res.data.es_id);
          setLatestEsId(res.data.es_id); // 保存最新的esid
          setIsEdit(res.data.can_edit);
          setCurrent_time(res.data.current_time);
          // 保存 daypart_ads_adjustments 数据
          setDaypartAdsAdjustments(res.data.daypart_ads_adjustments);

          // 处理版本信息 - 只在这里设置版本
          processVersionInfo(res.data.es_id);
        }
      } catch (error) {
        console.error('获取策略数据失败:', error);
        message.error('获取数据失败，请重试');
      }
    }
  };

  useEffect(() => {
    fetchStrategyData();
  }, [asin, profile_id, job_id, target_job_id, country, date]); // Added country to dependencies

  // 版本切换处理函数 - 基于最新esid构建目标esid
  const handleVersionChange = async (version: number) => {
    if (!latestEsId) return;

    const parts = latestEsId.split('_');
    if (parts.length > 1) {
      const targetEsId = `${parts[0]}_${version}`;
      setEs_id(targetEsId);

      try {
        const res = await getRoleAgentDetail({ es_id: targetEsId });
        if (res.code === 200) {
          const responseData = res.data as any;
          if (responseData.result && responseData.result.ads_strategy_day) {
            setDayStrategyData(responseData.result.ads_strategy_day);
            setReal_ads_result(responseData.result.real_ads_result);
            setCurrentVersion(version);
            setIsEdit(responseData.can_edit);
            setCurrent_time(responseData.current_time);

            // 版本选项保持不变，因为它们只基于最新esid
            // setVersionOptions 不需要在这里调用
          }
        } else {
          message.error('获取版本数据失败');
        }
      } catch (error) {
        console.error('获取版本数据失败:', error);
        message.error('获取版本数据失败，请重试');
      }
    }
  };

  const handleConfirmChanges = async () => {
    if (!current_time || !asin || !job_id || !profile_id || !country) {
      message.error("缺少必要的参数，无法提交");
      return;
    }
    await approveReportWithMessageId({
      parent_asin: parent_asin || asin || '',
      profile_id: profile_id,
      country: country,
      es_id: es_id,
      job_id: job_id,
      asins: asins || [asin],
    })
    // 更新全局状态
    updateAlertFn(true);
    onSuccess()

    // 如果没有AI反馈内容，直接关闭弹框
    if (!aiFeedbackContent.trim()) {
      if (onCancel) onCancel(false);
      return;
    }

    // 如果有AI反馈内容，将其保存到dayStrategyData中
    const updatedDayStrategyData = { ...dayStrategyData };
    if (aiFeedbackContent.trim()) {
      updatedDayStrategyData.ai_feedbackContent = aiFeedbackContent.trim();
    }

    const payload = {
      es_id: esId || es_id || '',
      current_time: current_time,
      parent_asin: parent_asin || asin || '',
      asins: asins || [asin],
      role: role || '',
      job_id: job_id,
      profile_id: profile_id,
      country: country || '',
      data: {
        ads_strategy_day: updatedDayStrategyData,
      },
    };
    try {
      const res: any = await editReport(payload);
      if (res.code === 200) {
        message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
      } else {
        message.error(res.message);
      }
    } catch (error) {
      message.error('提交修改失败，请重试');
    }
  };

  const placementColumns = [
    {
      title: '广告位类型',
      dataIndex: 'placement_type',
      key: 'placement_type',
      width: '15%'
    },
    {
      title: '今日溢价',
      dataIndex: 'new_bid',
      key: 'new_bid',
      width: '10%',
      render: (text: number) => {
        if (text === null || text === undefined) return '-';
        const value = text * 100;
        return `${value.toFixed(0)}%`;
      }
    },
    {
      title: '昨日溢价',
      dataIndex: 'old_bid',
      key: 'old_bid',
      width: '10%',
      render: (text: number) => {
        if (text === null || text === undefined) return '-';
        const value = text * 100;
        return `${value.toFixed(0)}%`;
      }
    },
    {
      title: '变化',
      key: 'bid_change',
      width: '10%',
      render: (record: any) => {
        if (record.old_bid === null || record.old_bid === undefined ||
          record.new_bid === null || record.new_bid === undefined) return '-';
        const change = (record.new_bid - record.old_bid) * 100;
        let color = 'blue';
        if (change > 5) {
          color = 'red';
        } else if (change < 0) {
          color = 'green';
        }
        return (
          <Tag color={color}>{change > 0 ? '+' : ''}{change.toFixed(0)}%</Tag>
        );
      }
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale'
    }
  ];

  // 检查小时竞价数据是否有效
  const hasValidHodData = dayStrategyData?.hod_bid_adjustment && dayStrategyData.hod_bid_adjustment.length > 0;

  const baseColumns = [
    {
      title: '广告类型',
      dataIndex: 'campaign_type',
      key: 'campaign_type',
      width: 90,
      fixed: 'left' as const,
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        let color = 'blue';
        if (text === 'sb') {
          color = 'red';
        } else if (text === 'sd') {
          color = 'green';
        }
        return (
          <Tag color={color}>{text.toUpperCase()}</Tag>
        );
      }
    },
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      width: 220,
      fixed: 'left' as const
    },
    {
      title: '近14天ACoS',
      dataIndex: 'l14d_acos',
      key: 'l14acos',
      width: 140,
      sorter: (a: DayStrategyData['campaign_budget'][0], b: DayStrategyData['campaign_budget'][0]) => {
        const aValue = a.l14d_acos ? parseFloat(a.l14d_acos.toString().replace('%', '')) : 0;
        const bValue = b.l14d_acos ? parseFloat(b.l14d_acos.toString().replace('%', '')) : 0;
        return aValue - bValue;
      },
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        return processNumberOrString(text, '%');
      }
    },
    {
      title: '历史同期ACoS',
      dataIndex: 'swd_acos',
      key: 'swd_acos',
      width: 150,
      sorter: (a: DayStrategyData['campaign_budget'][0], b: DayStrategyData['campaign_budget'][0]) => {
        const aValue = a.swd_acos ? parseFloat(a.swd_acos.toString().replace('%', '')) : 0;
        const bValue = b.swd_acos ? parseFloat(b.swd_acos.toString().replace('%', '')) : 0;
        return aValue - bValue;
      },
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        return processNumberOrString(text, '%');
      }
    },
    {
      title: '今日预算',
      dataIndex: 'amount',
      key: 'amount',
      width: 110,
      sorter: (a: DayStrategyData['campaign_budget'][0], b: DayStrategyData['campaign_budget'][0]) => (a.amount || 0) - (b.amount || 0),
      render: (amount: number) => {
        if (amount === null || amount === undefined) return '-';
        return `$${amount.toFixed(2)}`;
      }
    },
    {
      title: '昨日预算',
      dataIndex: 'benchmark_budget',
      key: 'benchmark_budget',
      width: 110,
      sorter: (a: DayStrategyData['campaign_budget'][0], b: DayStrategyData['campaign_budget'][0]) => (a.benchmark_budget || 0) - (b.benchmark_budget || 0),
      render: (benchmarkBudget: number) => {
        if (benchmarkBudget === null || benchmarkBudget === undefined) return '-';
        return benchmarkBudget ? `$${Number(benchmarkBudget).toFixed(2)}` : '-';
      }
    },
    {
      title: '变化',
      key: 'budget_change',
      width: 90,
      sorter: (a: DayStrategyData['campaign_budget'][0], b: DayStrategyData['campaign_budget'][0]) => {
        const changeA = a.benchmark_budget ? (a.amount - a.benchmark_budget) : 0;
        const changeB = b.benchmark_budget ? (b.amount - b.benchmark_budget) : 0;
        return changeA - changeB;
      },
      render: (record: any) => {
        if (!record.benchmark_budget) return '-';
        const change = record.amount - record.benchmark_budget;
        const changeText = change > 0 ? `+$${change.toFixed(2)}` : `$${change.toFixed(2)}`;
        let color = 'blue';
        if (change > 0) {
          color = 'red';
        } else if (change < 0) {
          color = 'green';
        }
        return (
          <Tag color={color}>{changeText}</Tag>
        );
      }
    },
    {
      title: '竞价调整',
      key: 'bid_adjustment',
      width: 150,
      render: (_: any, record: DayStrategyData['campaign_budget'][0]) => {
        if (!record.bid_adjustment) return '-';
        const bid_adjustment = Number(record.bid_adjustment);
        let color = 'blue';
        if (bid_adjustment > 0) {
          color = 'red';
        } else if (bid_adjustment < 0) {
          color = 'green';
        }
        return (
          <Tag color={color}>{processNumberOrString(bid_adjustment, '%')}</Tag>
        );
      }
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      width: 400
    },
  ];

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>



        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px', marginBottom: '24px', gap: 32 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            {
              showDateNavigator &&
              <DateNavigator
                initialDate={date || ''}
                onStrategyDataUpdate={handleStrategyDataUpdate} // Pass the new callback
                asin={asin || parent_asin}
                profile_id={profile_id}
                job_id={'ads_strategy_day'}
                dateType='single'
              />
            }
          </div>
          {
            (showHistoryVersion && maxVersion > 0 || controlBtn) &&
            <div>
              <Space size={16}>
                {showHistoryVersion && maxVersion > 0 && (
                  <div>
                    <span style={{ fontSize: '14px', color: '#666' }}>修订历史： </span>
                    <Select
                      value={currentVersion}
                      onChange={handleVersionChange}
                      options={versionOptions}
                      style={{ width: 90 }}
                    />
                  </div>
                )}
                {isEdit && controlBtn}
              </Space>
            </div>
          }
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin || asin || '',
              profile_id: profile_id || '',
              job_id: job_id || '',
              es_id: es_id || '',
              current_time: current_time || '',
            }}
          />
        </div>

        {!dayStrategyData ? <EmptyState
          imageWidth={180}
          text="未查到对应日期的报告"
        />
          :
          <>
            {/* 最新调整 Card */}
            {hasValidAdjustmentData(daypartAdsAdjustments) && (
              <Card
                className="card"
                style={{ marginTop: "24px", marginBottom: '24px' }}
                title={
                  <Flex align="center" justify="space-between">
                    <Flex align="center" gap={8}>
                      <span>最新调整</span>
                      <div style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        backgroundColor: '#ff4d4f'
                      }} />
                    </Flex>
                    <div>
                      {
                        daypartAdsAdjustments?.daypart_report !== null &&
                        <Button
                          type="link"
                          style={{ padding: 0, height: 'auto' }}
                          onClick={() => setIsDetailModalVisible(true)}
                        >
                          查看详情
                        </Button>
                      }
                    </div>
                  </Flex>
                }
              >
                {daypartAdsAdjustments && (
                  <div>
                    <Text style={{ color: '#1890ff', fontSize: '14px' }}>
                      广告优化调整
                      {
                        daypartAdsAdjustments.daypart_report?.current_time ?
                          ` (${daypartAdsAdjustments.daypart_report?.current_time?.split(' ')[0]} ${daypartAdsAdjustments.daypart_report?.current_time?.split(' ')[1]} ${country})`
                          :
                          ` (${dayStrategyData.date} ${country})`
                      }
                    </Text>
                    <ul className="goalList" style={{ marginTop: '12px' }}>
                      {daypartAdsAdjustments.amount && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            {
                              daypartAdsAdjustments.amount.old === undefined || daypartAdsAdjustments.amount.old === null ?
                                `今日预算：$${daypartAdsAdjustments.amount.new}`
                                :
                                `预算调整：$${daypartAdsAdjustments.amount.old ?? '-'} → $${daypartAdsAdjustments.amount.new ?? '-'}`
                            }
                          </div>
                        </li>
                      )}
                      {daypartAdsAdjustments.set_campaign_budget?.set_campaign_budget && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            Campaign预算：{daypartAdsAdjustments.set_campaign_budget.set_campaign_budget}
                          </div>
                        </li>
                      )}
                      {daypartAdsAdjustments.set_placement_bidding?.set_placement_bidding && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            广告位竞价：{daypartAdsAdjustments.set_placement_bidding.set_placement_bidding}
                          </div>
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </Card>
            )}

            <WeeklyProgressAnalysis data={dayStrategyData?.weekly_progress_analysis} />

            <Title level={3} className={styles.title} style={{ marginTop: "48px" }}>投放策略和预算</Title>
            <DayStrategyCard
              dayStrategyData={dayStrategyData}
              isEdit={isEdit}
            />

            <Title level={3} className={styles.title} style={{ marginTop: "48px" }}>预期结果</Title>
            <DayStrategyExpectedResults expected_results={dayStrategyData.expected_results} real_ads_result={_real_ads_result} />

            <Title level={3} className={styles.title} style={{ marginTop: "48px" }}>Campaign预算规划</Title>
            <Paragraph>*实际投放时，AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价</Paragraph>
            <Card className="card">
              {dayStrategyData.campaign_budget && dayStrategyData.campaign_budget.length > 0 ? (
                <Table
                  dataSource={dayStrategyData.campaign_budget}
                  columns={baseColumns}
                  rowKey="campaign_id"
                  pagination={false}
                  scroll={{ x: 1280 }}
                />
              ) : (
                <Empty description="暂无预算规划数据" />
              )}
            </Card>

            <Flex justify='space-between' align='center' style={{ marginTop: "48px" }}>
              <Title level={3} className={styles.title} style={{ margin: 0 }}>分时竞价策略</Title>
            </Flex>
            <Card className="card" style={{ marginTop: "16px" }}>
              {hasValidHodData ? (
                <>
                  <HodChart hodBidding={dayStrategyData.hod_bid_adjustment} />
                </>
              ) : (
                <Empty description="暂无分时竞价数据" />
              )}
            </Card>

            <Flex justify='space-between' align='center' style={{ marginTop: "48px" }}>
              <Title level={3} className={styles.title} style={{ margin: 0 }}>广告位竞价策略</Title>
            </Flex>
            <Card className="card" style={{ marginTop: "16px" }}>
              {dayStrategyData.placement_bid_adjustment && dayStrategyData.placement_bid_adjustment.length > 0 ? (
                <Table
                  dataSource={dayStrategyData.placement_bid_adjustment}
                  columns={placementColumns}
                  rowKey="placement_type"
                  pagination={false}
                // scroll={{ x: 1280 }}
                />
              ) : (
                <Empty description="暂无广告位竞价数据" />
              )}
            </Card>

            {/* 修订记录 */}
            <RevisionHistory
              revision_history={dayStrategyData?.revision_history}
            />
          </>
        }
      </div >

      {showReviewArea && isEdit && shouldShowConfirmationSection(country || '', 'day') && (
        <ConfirmationSection
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onSubmit={handleConfirmChanges}
          minRows={5}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
例如：
● 将总体投放策略修改为激进
● 今天的销售目标是 1000 美元，基于这个目标制定策略
● AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价'
        />
      )}

      {/* 详情弹窗 */}
      <Modal
        title={<Title level={2} style={{ margin: 0 }}>
          广告优化调整
          {daypartAdsAdjustments?.daypart_report?.current_time && `(${daypartAdsAdjustments.daypart_report.current_time} ${getDayOfWeek(daypartAdsAdjustments.daypart_report?.current_time)})`}
        </Title>}
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        width="90%"
        footer={null}
        destroyOnClose
        className='report-modal'
      >
        {daypartAdsAdjustments && (
          <DayStrategyCatContent
            asin={parent_asin}
            profile_id={profile_id}
            job_id={daypartAdsAdjustments.daypart_report?.job_id}
            current_time={daypartAdsAdjustments.daypart_report?.current_time}
            target_job_id={target_job_id}
            country={country}
          />
        )}
      </Modal>
    </div >
  );
};

export default DayStrategyContent;
