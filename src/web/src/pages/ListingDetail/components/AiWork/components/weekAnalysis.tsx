import React from 'react';
import { Row, Col, Card, Typography, Statistic, Tag, Space, message, Flex, Select, Table, Spin } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import { RiseOutlined, FallOutlined } from '@ant-design/icons';
import styles from './index.less';
import { getBaseReport, editReport, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import { useState, useEffect } from 'react';
import { approveReportWithMessageId, shouldShowConfirmationSection, generateVersionOptions } from '@/utils/bus';
import WeekAnalysisCard from '../../WeekAnalysisCard';

import MonthlyTrends, { monthly_trends } from '../../MonthlyTrends';
import RevisionHistory, { Revision_history } from './RevisionHistory';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import DateNavigator from './DateNavigator';

const { Title, Text } = Typography;

export interface Weekly_strategy {
  week_start_date: string;
  strategy: string;
}

export interface WeekAnalysisData {
  start_date: string;
  end_date: string;
  forecast?: {
    market_preview: string[];
    metrics_forecast: {
      traffic: 'up' | 'down' | 'stable';
      spend: 'up' | 'down' | 'stable';
      sales: 'up' | 'down' | 'stable';
      acos: 'up' | 'down' | 'stable';
      cvr: 'up' | 'down' | 'stable';
    };
    overview: string;
  };
  revision_history?: Revision_history;
  weekly_strategy: Weekly_strategy[];
  key_dates?: {
    start_date: string;
    end_date: string;
    type: string[];
    name: string;
    confidence: 'low' | 'medium' | 'high';
    significance: 'low' | 'medium' | 'high';
    expected_impact: {
      traffic: 'low' | 'medium' | 'high';
      conversion: 'low' | 'medium' | 'high';
      competition: 'low' | 'medium' | 'high';
      rationale?: string;
    };
    strategy: string;
  }[];
  swot: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };
  ads_suggestion: {
    approach: string;
    primary_goal: {
      goal: string;
      rationale: string;
    };
    other_goals: string[];
    rationale: string;
    weekly_strategy: Weekly_strategy[]
  };
  non_ads_suggestion: string[];
  google_searchs?: {
    rendered_content?: string;
    web_search_queries?: string[];
    grounding_chunks?: { title: string, url: string }[];
  };
}

interface WeekAnalysisContentProps {
  type: string;
  asin?: string;
  asins?: string[];
  parent_asin?: string;
  profile_id?: string;
  esId?: string;
  job_id?: string;
  current_time?: string;
  target_job_id?: string;
  role?: string;
  isEdit?: boolean;
  onCancel?: (refresh: boolean) => void;
  country?: string;
  showHistoryVersion?: boolean;
  showDateNavigator?: boolean;
  monthly_trends?: monthly_trends;
  start_date: string
  // 确定成功后执行的回调函数
  onSuccess: () => void;
  controlBtn?: React.ReactNode;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

// 市场分析报告组件
const WeekMonthAnalysisContent: React.FC<WeekAnalysisContentProps> = (props) => {
  const {
    type,
    asin,
    asins,
    job_id,
    target_job_id,
    onCancel = () => { },
    country,
    start_date,
    role,
    showHistoryVersion = true,
    showDateNavigator = true,
    onSuccess,
    controlBtn,
    showReviewArea = true
  } = props
  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const [weekAnalysisData, setWeekAnalysisData] = useState<WeekAnalysisData | null>(null);
  const [es_id, setEs_id] = useState<string>('');
  const [latestEsId, setLatestEsId] = useState<string>(''); // 专门存储当前日期最新的esid
  const [isEdit, setIsEdit] = useState<boolean>(props.isEdit ?? false);
  const [current_time, setCurrent_time] = useState(props.current_time);
  // 版本管理状态
  const [currentVersion, setCurrentVersion] = useState<number>(1);
  const [maxVersion, setMaxVersion] = useState<number>(1);
  const [versionOptions, setVersionOptions] = useState<{ label: string, value: number }[]>([]);
  const [loading, setLoading] = useState(false)
  // 处理版本信息的独立方法 - 基于最新的esid处理版本
  const processVersionInfo = (esId: string) => {
    if (!esId) return;

    const parts = esId.split('_');
    if (parts.length > 1) {
      const version = parseInt(parts[1]);
      setMaxVersion(version); // 设置最大版本号
      setVersionOptions(generateVersionOptions(version)); // 版本选项只基于最新esid
      setCurrentVersion(version); // 设置当前版本为最新版本
    }
  };

  const [google_searchs, setGoogleSearchs] = useState<{
    rendered_content?: string;
    web_search_queries?: string[];
    grounding_chunks?: { title: string, url: string }[];
  }>({
    rendered_content: '',
    web_search_queries: [],
    grounding_chunks: []
  });
  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');

  // 处理导航器数据更新的回调函数
  const handleStrategyDataUpdate = (data: any | null) => {
    if (data && data.result) {
      setEs_id(data.es_id);
      setLatestEsId(data.es_id); // 保存最新的esid
      setIsEdit(data.can_edit || false);
      processVersionInfo(data.es_id);
      setCurrent_time(data.current_time); // 设置当前时间

      // 根据 type 判断获取哪种类型的数据
      if (type === 'week' && data.result.market_report_week) {
        setWeekAnalysisData(data.result.market_report_week);
      } else if (type === 'month' && data.result.market_report_month) {
        setWeekAnalysisData(data.result.market_report_month);
      }

      if (data.google_searchs) {
        setGoogleSearchs({
          rendered_content: data.google_searchs.rendered_content,
          web_search_queries: data.google_searchs.web_search_queries,
          grounding_chunks: data.google_searchs.grounding_chunks
        });
      }
    } else {
      setWeekAnalysisData(null);
    }
  };

  // 获取市场分析数据
  const fetchWeekAnalysisData = async () => {
    // 优先通过接口获取数据
    if (asin && profile_id && job_id && current_time) {
      try {
        setLoading(true);
        const res: any = await getBaseReport({
          job_id,
          asin,
          profile_id,
          current_time,
          target_job_id
          // 注意：WeekAnalysisContent 不需要传 target_job_id 参数
        });
        setLoading(false);

        if (res.data && res.data.result) {
          setEs_id(res.data.es_id || '');
          setLatestEsId(res.data.es_id || ''); // 保存最新的esid
          setIsEdit(res.data.can_edit || false);
          setCurrent_time(res.data.current_time); // 设置当前时间

          // 根据 type 判断获取哪种类型的数据
          if (type === 'week' && res.data.result.market_report_week) {
            setWeekAnalysisData(res.data.result.market_report_week);
            if (res.data.google_searchs) {
              setGoogleSearchs({
                rendered_content: res.data.google_searchs.rendered_content,
                web_search_queries: res.data.google_searchs.web_search_queries,
                grounding_chunks: res.data.google_searchs.grounding_chunks
              });
            }

            // 处理版本信息 - 只在这里设置版本
            processVersionInfo(res.data.es_id || '');
            return;
          } else if (type === 'month' && res.data.result.market_report_month) {
            setWeekAnalysisData(res.data.result.market_report_month);
            if (res.data.google_searchs) {
              setGoogleSearchs({
                rendered_content: res.data.google_searchs.rendered_content,
                web_search_queries: res.data.google_searchs.web_search_queries,
                grounding_chunks: res.data.google_searchs.grounding_chunks
              });
            }

            // 处理版本信息 - 只在这里设置版本
            processVersionInfo(res.data.es_id || '');
            return;
          }
        }
      } catch (error) {
        console.error('获取市场分析数据失败:', error);
        message.error('获取数据失败，请重试');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchWeekAnalysisData();
  }, [asin, profile_id, job_id, props.current_time, type, props.esId]);

  // 版本切换处理函数 - 基于最新esid构建目标esid
  const handleVersionChange = async (version: number) => {
    setCurrentVersion(version);
    if (!latestEsId) return;

    const parts = latestEsId.split('_');
    if (parts.length > 1) {
      const targetEsId = `${parts[0]}_${version}`;
      setEs_id(targetEsId);

      try {
        const res = await getRoleAgentDetail({ es_id: targetEsId });
        if (res.code === 200) {
          const responseData = res.data as any;
          if (responseData.result) {
            // 根据 type 判断获取哪种类型的数据
            const reportKey = type === 'week' ? 'market_report_week' : 'market_report_month';
            if (responseData.result[reportKey]) {
              setWeekAnalysisData(responseData.result[reportKey]);
              setIsEdit(responseData.can_edit || false);
              setCurrent_time(responseData.current_time); // 设置当前时间

              // 版本选项保持不变，因为它们只基于最新esid
              // setVersionOptions 不需要在这里调用

              // 更新 google_searchs 数据
              if (responseData.google_searchs) {
                setGoogleSearchs({
                  rendered_content: responseData.google_searchs.rendered_content,
                  web_search_queries: responseData.google_searchs.web_search_queries,
                  grounding_chunks: responseData.google_searchs.grounding_chunks
                });
              }
            }
          }
        } else {
          message.error('获取版本数据失败');
        }
      } catch (error) {
        console.error('获取版本数据失败:', error);
        message.error('获取版本数据失败，请重试');
      }
    }
  };

  const handleConfirmChanges = async () => {
    if (!current_time || !asin || !job_id || !profile_id) {
      message.error("缺少必要的参数，无法提交");
      return;
    }

    try {
      await approveReportWithMessageId({
        parent_asin: parent_asin || asin || '',
        profile_id: profile_id,
        country: country || '',
        es_id: es_id || props.esId || '',
        job_id: job_id,
        asins: asins || [asin],
      })

      // 更新全局状态
      updateAlertFn(true);
      onSuccess()

      // 如果没有AI反馈内容，直接关闭弹框
      if (!aiFeedbackContent.trim()) {
        if (onCancel) onCancel(false);
        return;
      }

      const reportKey = type === 'week' ? 'market_report_week' : 'market_report_month';

      // 创建一个深拷贝的数据对象
      const updatedWeekAnalysisData = weekAnalysisData ? JSON.parse(JSON.stringify(weekAnalysisData)) : {};

      // 如果有AI反馈内容，保存到数据对象中
      if (aiFeedbackContent.trim()) {
        updatedWeekAnalysisData.ai_feedbackContent = aiFeedbackContent.trim();
      }

      const payload = {
        es_id: es_id || props.esId || '',
        current_time: current_time,
        parent_asin: parent_asin || asin || '',
        asins: asins || [asin],
        role: role || ' ',
        job_id: job_id,
        profile_id: profile_id,
        country: country || '',
        data: {
          [reportKey]: updatedWeekAnalysisData,
        },
      };
      const res: any = await editReport(payload);
      if (res && res.code === 200) {
        message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
      } else {
        message.error(res?.message || '修改提交失败');
      }
    } catch (error: any) {
      message.error('提交修改失败，请重试');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size='large'></Spin>
        <div style={{marginTop: 10}}>加载中...</div>
      </div>
    );
  }

  // 如果没有数据，显示空状态
  if (!weekAnalysisData || !weekAnalysisData.ads_suggestion) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div>暂无数据</div>
      </div>
    );
  }

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px', marginBottom: '24px', gap: 32 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            {
              showDateNavigator &&
              <DateNavigator
                initialDate={start_date}
                onStrategyDataUpdate={handleStrategyDataUpdate} // Pass the new callback
                asin={asin || parent_asin}
                profile_id={profile_id}
                job_id={'market_report_month'}
                dateType={job_id === 'market_report_month' ? 'range' : 'single'}
              />
            }
          </div>
          {
            (showHistoryVersion && maxVersion > 0 || controlBtn) &&
            <div>
              <Space size={16}>
                {showHistoryVersion && maxVersion > 0 && (
                  <div>
                    <span style={{ fontSize: '14px', color: '#666' }}>修订历史： </span>
                    <Select
                      value={currentVersion}
                      onChange={handleVersionChange}
                      options={versionOptions}
                      style={{ width: 90 }}
                    />
                  </div>
                )}
                {isEdit && controlBtn}
              </Space>
            </div>
          }
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin || asin || '',
              profile_id: profile_id || '',
              job_id: job_id || '',
              es_id: es_id || '',
              current_time: current_time || '',
            }}
            style={{ alignSelf: 'flex-end' }}
          />
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Card className="card">
              <Flex justify="space-between" align="center" style={{ marginBottom: "0.8em" }}>
                <Title level={4} style={{ margin: 0 }}>市场概述</Title>
              </Flex>
              <Text style={{ fontSize: "16px" }}>{weekAnalysisData.forecast?.overview}</Text>

            </Card>
          </Col>
          {
            props.monthly_trends &&
            <Col span={12}>
              <Card className="card">
                <Title level={4} style={{ margin: 0 }}>市场趋势分析 <span style={{ fontSize: 14, color: '#9d9d9d', fontWeight: 'normal' }}>反映市场整体趋势，并非本商品销售趋势</span> </Title>
                <div style={{ height: '160px' }}>
                  <MonthlyTrends country={country ?? ''} data={props.monthly_trends} />
                </div>
              </Card>
            </Col>
          }
        </Row>

        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} className={styles.title} style={{ margin: 0 }}>市场前瞻</Title>
        </Flex>
        <Row gutter={16}>
          <Col span={12}>
            <Card className="card" style={{ height: "100%" }}>
              <Flex justify="space-between" align="center">
                <Title level={4} className={styles.swotTitle} style={{ margin: 0 }}>趋势前瞻</Title>
              </Flex>
              <ul className="goalList" style={{ marginTop: "16px" }}>
                {weekAnalysisData.forecast?.market_preview.map((item, index) => (
                  <li key={index} className="goalItem">
                    <div className="goalDot"></div>
                    <Text className={styles.swotText}>{item}</Text>
                  </li>
                ))}
              </ul>

            </Card>
          </Col>
          <Col span={12}>
            <Card className='card' style={{ height: "100%" }}>
              <Flex justify="space-between" align="center">
                <Title level={4} className={styles.swotTitle} style={{ margin: 0 }}>关键指标预测</Title>
              </Flex>
              <Row style={{ marginTop: 24, alignContent: "center" }}>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>流量</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>销售</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>广告支出</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>ACoS</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
              </Row>

            </Card>
          </Col>
        </Row>

        {/* SWOT分析板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} className={styles.title} style={{ margin: 0 }}>SWOT 分析</Title>
        </Flex>
        <div className={styles.swotContainer}>
          <Row gutter={16} style={{ display: 'flex' }}>
            <Col span={12} style={{ display: 'flex' }}>
              <Card className={styles.swotCard} style={{ backgroundColor: '#e6f7ff', borderLeft: '4px solid #1890ff', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} className={styles.swotTitle} style={{ color: '#1890ff', margin: 0 }}>优势 (Strengths)</Title>
                </Flex>
                <div className={styles.swotContent} style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.strengths.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
            <Col span={12} style={{ display: 'flex' }}>
              <Card className={styles.swotCard} style={{ backgroundColor: '#fff1f0', borderLeft: '4px solid #f5222d', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} className={styles.swotTitle} style={{ color: '#f5222d', margin: 0 }}>劣势 (Weaknesses)</Title>
                </Flex>
                <div className={styles.swotContent} style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.weaknesses.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: '16px', display: 'flex' }}>
            <Col span={12} style={{ display: 'flex' }}>
              <Card className={styles.swotCard} style={{ backgroundColor: '#f6ffed', borderLeft: '4px solid #52c41a', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} className={styles.swotTitle} style={{ color: '#52c41a', margin: 0 }}>机会 (Opportunities)</Title>
                </Flex>
                <div className={styles.swotContent} style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.opportunities.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
            <Col span={12} style={{ display: 'flex' }}>
              <Card className={styles.swotCard} style={{ backgroundColor: '#fff2e8', borderLeft: '4px solid #fa8c16', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} className={styles.swotTitle} style={{ color: '#fa8c16', margin: 0 }}>威胁 (Threats)</Title>
                </Flex>
                <div className={styles.swotContent} style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.threats.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 关键日期板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} className={styles.title} style={{ margin: 0 }}>关键日期</Title>
        </Flex>
        <Space direction="vertical" size={'middle'} style={{ width: '100%' }}>
          {weekAnalysisData.key_dates && weekAnalysisData.key_dates.length > 0 ? (
            weekAnalysisData.key_dates?.map((date, index) => (
              <Card className="card" key={index}>
                <Row gutter={4} style={{ marginBottom: 24 }}>
                  <Col span={24}>
                    <Flex justify="space-between" align="center">
                      <Title level={4} style={{ margin: 0 }}>{date.name}</Title>
                    </Flex>
                    <Text style={{ fontSize: "14px", color: '#666', marginTop: 8, display: 'block' }}>{date.expected_impact.rationale}</Text>
                  </Col>
                </Row>
                <Row gutter={[24, 16]}>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>日期范围</Text>
                    </div>
                    <Text style={{ fontSize: "16px" }}>{date.start_date} ~ {date.end_date}</Text>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>类型</Text>
                    </div>
                    <Space wrap>
                      {date.type.map((type, i) => (
                        <Tag style={{ fontSize: "16px" }} key={i} color={type === 'peak' ? 'red' : 'blue'}>
                          {type === 'peak' ? '高峰期' : type === 'other' ? '其他' : type}
                        </Tag>
                      ))}
                    </Space>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>重要性</Text>
                    </div>
                    <Tag style={{ fontSize: "16px" }} color={date.significance === 'high' ? 'red' : date.significance === 'medium' ? 'orange' : 'blue'}>
                      {date.significance === 'high' ? '高' : date.significance === 'medium' ? '中' : '低'}
                    </Tag>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>预期影响</Text>
                    </div>
                    <Space wrap>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.traffic === 'high' ? 'red' : date.expected_impact.traffic === 'medium' ? 'orange' : 'blue'}>
                        流量: {date.expected_impact.traffic === 'high' ? '高' : date.expected_impact.traffic === 'medium' ? '中' : '低'}
                      </Tag>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.conversion === 'high' ? 'red' : date.expected_impact.conversion === 'medium' ? 'orange' : 'blue'}>
                        转化: {date.expected_impact.conversion === 'high' ? '高' : date.expected_impact.conversion === 'medium' ? '中' : '低'}
                      </Tag>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.competition === 'high' ? 'red' : date.expected_impact.competition === 'medium' ? 'orange' : 'blue'}>
                        竞争: {date.expected_impact.competition === 'high' ? '高' : date.expected_impact.competition === 'medium' ? '中' : '低'}
                      </Tag>
                    </Space>
                  </Col>
                </Row>
                <Row style={{ marginTop: 24 }}>
                  <Col span={24}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>策略建议</Text>
                    </div>
                    <Text style={{ fontSize: "16px" }}>{date.strategy}</Text>
                  </Col>
                </Row>

              </Card>
            ))
          ) : (
            <div style={{ padding: '24px 0px 24px 0px' }}>
              <Text style={{ fontSize: '16px', color: '#666' }}>本周没有需要重点关注的特殊日期</Text>
            </div>
          )}
        </Space>

        {/* 投放策略板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} className={styles.title} style={{ margin: 0 }}>广告策略建议</Title>
        </Flex>

        <WeekAnalysisCard
          ads_suggestion={weekAnalysisData.ads_suggestion}
          revision_history={weekAnalysisData.revision_history}
        />

        <Card className='card' style={{ marginTop: '16px' }}>
          <Flex justify="space-between" align="center">
            <Title level={4} style={{ margin: 0 }}>每周投放策略</Title>
          </Flex>
          <Table style={{ marginTop: 16 }} columns={[
            {
              title: '日期',
              dataIndex: 'week_start_date',
              key: 'week_start_date',
              width: '200px',
            },
            {
              title: '策略',
              dataIndex: 'strategy',
              key: 'strategy',
            },
          ]} dataSource={weekAnalysisData.ads_suggestion.weekly_strategy} pagination={false} />

        </Card>

        {/* 运营建议板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>运营建议</Title>
        </Flex>
        <Card className='card'>
          <ul className="goalList">
            {weekAnalysisData.non_ads_suggestion?.map((item, index) => (
              <li key={index} className="goalItem">
                <div className="goalDot"></div>
                <Text className={styles.swotText}>{item}</Text>
              </li>
            ))}
          </ul>

        </Card>

        {/* 参考链接 */}
        {
          (google_searchs.grounding_chunks && google_searchs.grounding_chunks.length > 0) && <>
            <Flex justify="space-between" align="center" style={{ marginTop: "48px" }}>
              <Title level={3} className={styles.title} style={{ margin: 0 }}>参考链接</Title>
            </Flex>
            <Card className={styles.card}>
              {
                google_searchs.grounding_chunks?.map((item, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    {index + 1}.
                    <a href={item.url} target="_blank" rel="noopener noreferrer" style={{ marginLeft: 8 }}>{item.title}</a>
                  </div>
                ))
              }
            </Card>
          </>
        }

        {/* 相关搜索 */}
        {
          google_searchs.rendered_content && <>
            <Flex justify="space-between" align="center" style={{ marginTop: "48px" }}>
              <Title level={3} className={styles.title} style={{ margin: 0 }}>相关搜索</Title>
            </Flex>
            <Card className={styles.card}>
              <div dangerouslySetInnerHTML={{
                __html: google_searchs.rendered_content.replace(
                  /<a /g,
                  '<a target="_blank" rel="noopener noreferrer" '
                )
              }} />
            </Card>
          </>
        }



        {/* 修订记录 */}
        <RevisionHistory
          revision_history={weekAnalysisData?.revision_history}
        />

      </div>

      {showReviewArea && isEdit && shouldShowConfirmationSection(country || '', 'month') && (
        <ConfirmationSection
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onSubmit={handleConfirmChanges}
          minRows={5}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
例如：
● 7月到10月为本产品的销售旺季，市场流量上升
● 将总体策略更改为激进
● 主要目标销售额增长25%，ACOS控制在30%以内
● 基于上述建议重新制定市场报告和广告策略'
        />
      )}
    </div>
  );
};

export default WeekMonthAnalysisContent;
